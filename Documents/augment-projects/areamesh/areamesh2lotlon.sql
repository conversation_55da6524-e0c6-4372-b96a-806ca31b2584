CREATE OR REPLACE FUNCTION `beaconbank-analytics.UDF.areamesh2latlon_finer`(meshcode STRING) RETURNS ARRAY<STRUCT<meshcode STRING, mesh_scale STRING, center_lat FLOAT64, center_lon FLOAT64, boundary_lat_min FLOAT64, boundary_lon_min FLOAT64, boundary_lat_max FLOAT64, boundary_lon_max FLOAT64>> AS (
(

  (
SELECT ARRAY(
  SELECT AS STRUCT
    meshcode,
    CASE LENGTH(meshcode)
      WHEN 11 THEN '1/8次メッシュ（約125m）'
      WHEN 10 THEN '五次メッシュ（約250m）'
      WHEN 9 THEN '四次メッシュ（約500m）'
      WHEN 8 THEN '三次メッシュ（約1km）'
      WHEN 6 THEN '二次メッシュ（約10km）'
      WHEN 4 THEN '一次メッシュ（約80km）'
    ELSE NULL END mesh_scale,
    CASE LENGTH(meshcode)
      WHEN 11 THEN
        -- Center Latitude calculation for 11-digit 1/8次メッシュ（125m）
        (CAST(LEFT(meshcode,2) AS FLOAT64) / 1.5) +                                   -- 1次メッシュから緯度復元
        (CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 5.0 / 60.0) +                       -- 2次メッシュ
        (CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2.5 / 60.0) +                       -- 3次メッシュ
        (CASE
          WHEN CAST(SUBSTR(meshcode, 9, 1) AS INT64) IN (1,2) THEN 0.625 / 60.0       -- 1/2次メッシュ下半分
          WHEN CAST(SUBSTR(meshcode, 9, 1) AS INT64) IN (3,4) THEN 1.875 / 60.0       -- 1/2次メッシュ上半分
          ELSE 0
        END) +
        (CASE
          WHEN CAST(SUBSTR(meshcode, 10, 1) AS INT64) IN (1,2) THEN 0.3125 / 60.0     -- 1/4次メッシュ下半分
          WHEN CAST(SUBSTR(meshcode, 10, 1) AS INT64) IN (3,4) THEN 0.9375 / 60.0     -- 1/4次メッシュ上半分
          ELSE 0
        END) +
        (CASE
          WHEN CAST(SUBSTR(meshcode, 11, 1) AS INT64) IN (1,2) THEN 0.15625 / 60.0    -- 1/8次メッシュ下半分
          WHEN CAST(SUBSTR(meshcode, 11, 1) AS INT64) IN (3,4) THEN 0.46875 / 60.0    -- 1/8次メッシュ上半分
          ELSE 0
        END)
      WHEN 10 THEN
        CASE
          WHEN SUBSTR(meshcode, 9, 2) IN ("11","12","21","22") THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 1 / 8
          WHEN SUBSTR(meshcode, 9, 2) IN ("13","14","23","24") THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 3 / 8
          WHEN SUBSTR(meshcode, 9, 2) IN ("31","32","41","42") THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 5 / 8
          WHEN SUBSTR(meshcode, 9, 2) IN ("33","34","43","44") THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 7 / 8
        ELSE NULL END
      WHEN 9 THEN
        CASE SUBSTR(meshcode, 9, 1)
          WHEN '1' THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 1 / 4
          WHEN '2' THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 1 / 4
          WHEN '3' THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 3 / 4
          WHEN '4' THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 3 / 4
        ELSE NULL END
      WHEN 8 THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 1 / 2
      WHEN 6 THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + (2 / 3 / 8) * 1 / 2
      WHEN 4 THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + (2 / 3) * 1 / 2
    ELSE NULL END center_lat,
    CASE LENGTH(meshcode)
      WHEN 11 THEN
        -- Center Longitude calculation for 11-digit 1/8次メッシュ（125m）
        (CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100) +                               -- 1次メッシュから経度復元
        (CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) * 7.5 / 60.0) +                       -- 2次メッシュ
        (CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) * 0.75 / 60.0) +                      -- 3次メッシュ
        (CASE
          WHEN CAST(SUBSTR(meshcode, 9, 1) AS INT64) IN (1,3) THEN 0.1875 / 60.0      -- 1/2次メッシュ左半分
          WHEN CAST(SUBSTR(meshcode, 9, 1) AS INT64) IN (2,4) THEN 0.5625 / 60.0      -- 1/2次メッシュ右半分
          ELSE 0
        END) +
        (CASE
          WHEN CAST(SUBSTR(meshcode, 10, 1) AS INT64) IN (1,3) THEN 0.09375 / 60.0    -- 1/4次メッシュ左半分
          WHEN CAST(SUBSTR(meshcode, 10, 1) AS INT64) IN (2,4) THEN 0.28125 / 60.0    -- 1/4次メッシュ右半分
          ELSE 0
        END) +
        (CASE
          WHEN CAST(SUBSTR(meshcode, 11, 1) AS INT64) IN (1,3) THEN 0.046875 / 60.0   -- 1/8次メッシュ左半分
          WHEN CAST(SUBSTR(meshcode, 11, 1) AS INT64) IN (2,4) THEN 0.140625 / 60.0   -- 1/8次メッシュ右半分
          ELSE 0
        END)
      WHEN 10 THEN
        CASE
          WHEN SUBSTR(meshcode, 9, 2) IN("33","31","13","11") THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 1 / 8
          WHEN SUBSTR(meshcode, 9, 2) IN("34","32","14","12") THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 3 / 8
          WHEN SUBSTR(meshcode, 9, 2) IN("43","41","23","21") THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 5 / 8
          WHEN SUBSTR(meshcode, 9, 2) IN("44","42","24","22") THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 7 / 8
        ELSE NULL END
      WHEN 9 THEN
        CASE SUBSTR(meshcode, 9, 1)
          WHEN '1' THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 1 / 4
          WHEN '2' THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 3 / 4
          WHEN '3' THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 1 / 4
          WHEN '4' THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 3 / 4
        ELSE NULL END
      WHEN 8 THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 1 / 2
      WHEN 6 THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + (1 / 8) * 1 / 2
      WHEN 4 THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + 1 / 2
    ELSE NULL END center_lon,

    CASE LENGTH(meshcode)
      WHEN 11 THEN
        -- Boundary Latitude Min for 11-digit 1/8次メッシュ（125m）
        -- Start from the base 1km mesh (8-digit) and subdivide
        CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 +
        CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 +
        CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 +
        -- Add subdivision offsets for 1/2, 1/4, and 1/8 mesh
        (CASE
          WHEN CAST(SUBSTR(meshcode, 9, 1) AS INT64) IN (1,2) THEN 0.0                -- 1/2次メッシュ下半分
          WHEN CAST(SUBSTR(meshcode, 9, 1) AS INT64) IN (3,4) THEN (2 / 3 / 8 / 10) * 1 / 2  -- 1/2次メッシュ上半分
          ELSE 0
        END) +
        (CASE
          WHEN CAST(SUBSTR(meshcode, 10, 1) AS INT64) IN (1,2) THEN 0.0               -- 1/4次メッシュ下半分
          WHEN CAST(SUBSTR(meshcode, 10, 1) AS INT64) IN (3,4) THEN (2 / 3 / 8 / 10) * 1 / 4  -- 1/4次メッシュ上半分
          ELSE 0
        END) +
        (CASE
          WHEN CAST(SUBSTR(meshcode, 11, 1) AS INT64) IN (1,2) THEN 0.0               -- 1/8次メッシュ下半分
          WHEN CAST(SUBSTR(meshcode, 11, 1) AS INT64) IN (3,4) THEN (2 / 3 / 8 / 10) * 1 / 8  -- 1/8次メッシュ上半分
          ELSE 0
        END)
      WHEN 10 THEN
      CASE
        WHEN SUBSTR(meshcode, 9, 2) IN ("11","12","21","22") THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10
        WHEN SUBSTR(meshcode, 9, 2) IN ("13","14","23","24") THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 2 / 8
        WHEN SUBSTR(meshcode, 9, 2) IN ("31","32","41","42") THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 4 / 8
        WHEN SUBSTR(meshcode, 9, 2) IN ("33","34","43","44") THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 6 / 8
      ELSE NULL END
      WHEN 9 THEN
        CASE SUBSTR(meshcode, 9, 1)
          WHEN '1' THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10
          WHEN '2' THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10
          WHEN '3' THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 1 / 2
          WHEN '4' THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 1 / 2
        ELSE NULL END
      WHEN 8 THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10
      WHEN 6 THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8
      WHEN 4 THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3
    ELSE NULL END boundary_lat_min,
    CASE LENGTH(meshcode)
      WHEN 11 THEN
        -- Boundary Longitude Min for 11-digit 1/8次メッシュ（125m）
        -- Start from the base 1km mesh (8-digit) and subdivide
        CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 +
        CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 +
        CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 +
        -- Add subdivision offsets for 1/2, 1/4, and 1/8 mesh
        (CASE
          WHEN CAST(SUBSTR(meshcode, 9, 1) AS INT64) IN (1,3) THEN 0.0                -- 1/2次メッシュ左半分
          WHEN CAST(SUBSTR(meshcode, 9, 1) AS INT64) IN (2,4) THEN (1 / 8 / 10) * 1 / 2  -- 1/2次メッシュ右半分
          ELSE 0
        END) +
        (CASE
          WHEN CAST(SUBSTR(meshcode, 10, 1) AS INT64) IN (1,3) THEN 0.0               -- 1/4次メッシュ左半分
          WHEN CAST(SUBSTR(meshcode, 10, 1) AS INT64) IN (2,4) THEN (1 / 8 / 10) * 1 / 4  -- 1/4次メッシュ右半分
          ELSE 0
        END) +
        (CASE
          WHEN CAST(SUBSTR(meshcode, 11, 1) AS INT64) IN (1,3) THEN 0.0               -- 1/8次メッシュ左半分
          WHEN CAST(SUBSTR(meshcode, 11, 1) AS INT64) IN (2,4) THEN (1 / 8 / 10) * 1 / 8  -- 1/8次メッシュ右半分
          ELSE 0
        END)
      WHEN 10 THEN
      CASE
        WHEN SUBSTR(meshcode, 9, 2) IN("33","31","13","11") THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10
        WHEN SUBSTR(meshcode, 9, 2) IN("34","32","14","12") THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 2 / 8
        WHEN SUBSTR(meshcode, 9, 2) IN("43","41","23","21") THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 4 / 8
        WHEN SUBSTR(meshcode, 9, 2) IN("44","42","24","22") THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 6 / 8
      ELSE NULL END
      WHEN 9 THEN
        CASE SUBSTR(meshcode, 9, 1)
          WHEN '1' THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10
          WHEN '2' THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 1 / 2
          WHEN '3' THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10
          WHEN '4' THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 1 / 2
        ELSE NULL END
      WHEN 8 THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10
      WHEN 6 THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8
      WHEN 4 THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100
    ELSE NULL END boundary_lon_min,

    CASE LENGTH(meshcode)
      WHEN 11 THEN
        -- Boundary Latitude Max for 11-digit 1/8次メッシュ（125m）
        -- Start from the base 1km mesh (8-digit) and subdivide, then add the cell height
        CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 +
        CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 +
        CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 +
        -- Add subdivision offsets for 1/2, 1/4, and 1/8 mesh, then add the cell height
        (CASE
          WHEN CAST(SUBSTR(meshcode, 9, 1) AS INT64) IN (1,2) THEN (2 / 3 / 8 / 10) * 1 / 2  -- 1/2次メッシュ下半分 + height
          WHEN CAST(SUBSTR(meshcode, 9, 1) AS INT64) IN (3,4) THEN (2 / 3 / 8 / 10) * 1      -- 1/2次メッシュ上半分 + height
          ELSE 0
        END) +
        (CASE
          WHEN CAST(SUBSTR(meshcode, 10, 1) AS INT64) IN (1,2) THEN (2 / 3 / 8 / 10) * 1 / 4  -- 1/4次メッシュ下半分 + height
          WHEN CAST(SUBSTR(meshcode, 10, 1) AS INT64) IN (3,4) THEN (2 / 3 / 8 / 10) * 1 / 2  -- 1/4次メッシュ上半分 + height
          ELSE 0
        END) +
        (CASE
          WHEN CAST(SUBSTR(meshcode, 11, 1) AS INT64) IN (1,2) THEN (2 / 3 / 8 / 10) * 1 / 8  -- 1/8次メッシュ下半分 + height
          WHEN CAST(SUBSTR(meshcode, 11, 1) AS INT64) IN (3,4) THEN (2 / 3 / 8 / 10) * 1 / 4  -- 1/8次メッシュ上半分 + height
          ELSE 0
        END)
      WHEN 10 THEN
      CASE
        WHEN SUBSTR(meshcode, 9, 2) IN ("11","12","21","22") THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 2 / 8
        WHEN SUBSTR(meshcode, 9, 2) IN ("13","14","23","24") THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 4 / 8
        WHEN SUBSTR(meshcode, 9, 2) IN ("31","32","41","42") THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 6 / 8
        WHEN SUBSTR(meshcode, 9, 2) IN ("33","34","43","44") THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 8 / 8
      ELSE NULL END
      WHEN 9 THEN
        CASE SUBSTR(meshcode, 9, 1)
          WHEN '1' THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 1 / 2
          WHEN '2' THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10) * 1 / 2
          WHEN '3' THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10)
          WHEN '4' THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10)
        ELSE NULL END
      WHEN 8 THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + CAST(SUBSTR(meshcode, 7,1) AS FLOAT64) * 2 / 3 / 8 / 10 + (2 / 3 / 8 / 10)
      WHEN 6 THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + CAST(SUBSTR(meshcode, 5,1) AS FLOAT64) * 2 / 3 / 8 + (2 / 3 / 8)
      WHEN 4 THEN CAST(LEFT(meshcode,2) AS FLOAT64) * 2 / 3 + (2 / 3)
    ELSE NULL END boundary_lat_max,
    CASE LENGTH(meshcode)
      WHEN 11 THEN
        -- Boundary Longitude Max for 11-digit 1/8次メッシュ（125m）
        -- Start from the base 1km mesh (8-digit) and subdivide, then add the cell width
        CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 +
        CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 +
        CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 +
        -- Add subdivision offsets for 1/2, 1/4, and 1/8 mesh, then add the cell width
        (CASE
          WHEN CAST(SUBSTR(meshcode, 9, 1) AS INT64) IN (1,3) THEN (1 / 8 / 10) * 1 / 2  -- 1/2次メッシュ左半分 + width
          WHEN CAST(SUBSTR(meshcode, 9, 1) AS INT64) IN (2,4) THEN (1 / 8 / 10) * 1      -- 1/2次メッシュ右半分 + width
          ELSE 0
        END) +
        (CASE
          WHEN CAST(SUBSTR(meshcode, 10, 1) AS INT64) IN (1,3) THEN (1 / 8 / 10) * 1 / 4  -- 1/4次メッシュ左半分 + width
          WHEN CAST(SUBSTR(meshcode, 10, 1) AS INT64) IN (2,4) THEN (1 / 8 / 10) * 1 / 2  -- 1/4次メッシュ右半分 + width
          ELSE 0
        END) +
        (CASE
          WHEN CAST(SUBSTR(meshcode, 11, 1) AS INT64) IN (1,3) THEN (1 / 8 / 10) * 1 / 8  -- 1/8次メッシュ左半分 + width
          WHEN CAST(SUBSTR(meshcode, 11, 1) AS INT64) IN (2,4) THEN (1 / 8 / 10) * 1 / 4  -- 1/8次メッシュ右半分 + width
          ELSE 0
        END)
      WHEN 10 THEN
      CASE
        WHEN SUBSTR(meshcode, 9, 2) IN("33","31","13","11") THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 2 / 8
        WHEN SUBSTR(meshcode, 9, 2) IN("34","32","14","12") THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 4 / 8
        WHEN SUBSTR(meshcode, 9, 2) IN("43","41","23","21") THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 6 / 8
        WHEN SUBSTR(meshcode, 9, 2) IN("44","42","24","22") THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 8 / 8
      ELSE NULL END
      WHEN 9 THEN
        CASE SUBSTR(meshcode, 9, 1)
          WHEN '1' THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 1 / 2
          WHEN '2' THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10)
          WHEN '3' THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10) * 1 / 2
          WHEN '4' THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10)
        ELSE NULL END
      WHEN 8 THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + CAST(SUBSTR(meshcode, 8,1) AS FLOAT64) / 8 / 10 + (1 / 8 / 10)
      WHEN 6 THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + CAST(SUBSTR(meshcode, 6,1) AS FLOAT64) / 8 + (1 / 8)
      WHEN 4 THEN CAST(SUBSTR(meshcode,3,2) AS FLOAT64) + 100 + 1
    ELSE NULL END boundary_lon_max,
  )
)
)
);