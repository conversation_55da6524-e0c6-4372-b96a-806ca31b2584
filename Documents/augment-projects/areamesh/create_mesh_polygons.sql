-- S<PERSON> to create polygons from area mesh codes using the areamesh2latlon_finer UDF
-- This query converts mesh codes to polygon geometries using the boundary coordinates

WITH mesh_data AS (
  -- Sample mesh codes - replace this with your actual mesh code data
  SELECT meshcode FROM UNNEST([
    '533945',      -- 二次メッシュ（約10km）
    '53394511',    -- 三次メッシュ（約1km）
    '*********',   -- 四次メッシュ（約500m）
    '**********',  -- 五次メッシュ（約250m）
    '***********'  -- 1/8次メッシュ（約125m）
  ]) AS meshcode
),

mesh_coordinates AS (
  SELECT 
    meshcode,
    coord_data.mesh_scale,
    coord_data.center_lat,
    coord_data.center_lon,
    coord_data.boundary_lat_min,
    coord_data.boundary_lon_min,
    coord_data.boundary_lat_max,
    coord_data.boundary_lon_max
  FROM mesh_data,
  UNNEST(`beaconbank-analytics.UDF.areamesh2latlon_finer`(meshcode)) AS coord_data
)

SELECT 
  meshcode,
  mesh_scale,
  center_lat,
  center_lon,
  -- Create polygon geometry using ST_GEOGFROMTEXT with WKT format
  ST_GEOGFROMTEXT(
    CONCAT(
      'POLYGON((',
      CAST(boundary_lon_min AS STRING), ' ', CAST(boundary_lat_min AS STRING), ',',
      CAST(boundary_lon_max AS STRING), ' ', CAST(boundary_lat_min AS STRING), ',',
      CAST(boundary_lon_max AS STRING), ' ', CAST(boundary_lat_max AS STRING), ',',
      CAST(boundary_lon_min AS STRING), ' ', CAST(boundary_lat_max AS STRING), ',',
      CAST(boundary_lon_min AS STRING), ' ', CAST(boundary_lat_min AS STRING),
      '))'
    )
  ) AS mesh_polygon,
  
  -- Alternative: Create polygon using ST_GEOGFROMGEOJSON
  ST_GEOGFROMGEOJSON(
    JSON_EXTRACT(
      TO_JSON_STRING(
        STRUCT(
          'Polygon' AS type,
          [[[boundary_lon_min, boundary_lat_min],
            [boundary_lon_max, boundary_lat_min],
            [boundary_lon_max, boundary_lat_max],
            [boundary_lon_min, boundary_lat_max],
            [boundary_lon_min, boundary_lat_min]]] AS coordinates
        )
      ),
      '$'
    )
  ) AS mesh_polygon_geojson,
  
  -- Calculate area in square meters
  ST_AREA(
    ST_GEOGFROMTEXT(
      CONCAT(
        'POLYGON((',
        CAST(boundary_lon_min AS STRING), ' ', CAST(boundary_lat_min AS STRING), ',',
        CAST(boundary_lon_max AS STRING), ' ', CAST(boundary_lat_min AS STRING), ',',
        CAST(boundary_lon_max AS STRING), ' ', CAST(boundary_lat_max AS STRING), ',',
        CAST(boundary_lon_min AS STRING), ' ', CAST(boundary_lat_max AS STRING), ',',
        CAST(boundary_lon_min AS STRING), ' ', CAST(boundary_lat_min AS STRING),
        '))'
      )
    )
  ) AS area_square_meters,
  
  -- Boundary coordinates for reference
  boundary_lat_min,
  boundary_lon_min,
  boundary_lat_max,
  boundary_lon_max

FROM mesh_coordinates
ORDER BY meshcode;
