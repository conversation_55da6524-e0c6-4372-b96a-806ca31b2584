-- Advanced SQL to create polygons from area mesh codes with multiple output formats
-- Includes functions for batch processing and different geometry formats

-- Function to create a single mesh polygon
CREATE OR REPLACE FUNCTION `beaconbank-analytics.UDF.create_mesh_polygon`(meshcode STRING)
RETURNS GEOGRAPHY AS (
  (
    WITH mesh_coords AS (
      SELECT 
        coord_data.boundary_lat_min,
        coord_data.boundary_lon_min,
        coord_data.boundary_lat_max,
        coord_data.boundary_lon_max
      FROM UNNEST(`beaconbank-analytics.UDF.areamesh2latlon_finer`(meshcode)) AS coord_data
    )
    SELECT 
      ST_GEOGFROMTEXT(
        CONCAT(
          'POLYGON((',
          CAST(boundary_lon_min AS STRING), ' ', CAST(boundary_lat_min AS STRING), ',',
          CAST(boundary_lon_max AS STRING), ' ', CAST(boundary_lat_min AS STRING), ',',
          CAST(boundary_lon_max AS STRING), ' ', CAST(boundary_lat_max AS STRING), ',',
          CAST(boundary_lon_min AS STRING), ' ', CAST(boundary_lat_max AS STRING), ',',
          CAST(boundary_lon_min AS STRING), ' ', CAST(boundary_lat_min AS STRING),
          '))'
        )
      )
    FROM mesh_coords
  )
);

-- Function to get mesh polygon as GeoJSON
CREATE OR REPLACE FUNCTION `beaconbank-analytics.UDF.mesh_to_geojson`(meshcode STRING)
RETURNS STRING AS (
  (
    WITH mesh_coords AS (
      SELECT 
        coord_data.boundary_lat_min,
        coord_data.boundary_lon_min,
        coord_data.boundary_lat_max,
        coord_data.boundary_lon_max
      FROM UNNEST(`beaconbank-analytics.UDF.areamesh2latlon_finer`(meshcode)) AS coord_data
    )
    SELECT 
      TO_JSON_STRING(
        STRUCT(
          'Feature' AS type,
          STRUCT(
            'Polygon' AS type,
            [[[boundary_lon_min, boundary_lat_min],
              [boundary_lon_max, boundary_lat_min],
              [boundary_lon_max, boundary_lat_max],
              [boundary_lon_min, boundary_lat_max],
              [boundary_lon_min, boundary_lat_min]]] AS coordinates
          ) AS geometry,
          STRUCT(
            meshcode AS meshcode,
            LENGTH(meshcode) AS mesh_level
          ) AS properties
        )
      )
    FROM mesh_coords
  )
);

-- Example query: Create polygons from a table of mesh codes
-- Replace 'your_table' and 'your_meshcode_column' with your actual table and column names
/*
WITH mesh_polygons AS (
  SELECT 
    your_meshcode_column AS meshcode,
    `beaconbank-analytics.UDF.create_mesh_polygon`(your_meshcode_column) AS polygon,
    coord_data.mesh_scale,
    coord_data.center_lat,
    coord_data.center_lon,
    ST_AREA(`beaconbank-analytics.UDF.create_mesh_polygon`(your_meshcode_column)) AS area_sqm
  FROM your_table,
  UNNEST(`beaconbank-analytics.UDF.areamesh2latlon_finer`(your_meshcode_column)) AS coord_data
  WHERE your_meshcode_column IS NOT NULL
)

SELECT 
  meshcode,
  mesh_scale,
  center_lat,
  center_lon,
  polygon,
  area_sqm,
  ROUND(area_sqm / 1000000, 4) AS area_sqkm
FROM mesh_polygons
ORDER BY meshcode;
*/
