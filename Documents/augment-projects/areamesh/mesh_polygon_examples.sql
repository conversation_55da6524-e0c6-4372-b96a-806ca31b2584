-- Practical examples for creating mesh polygons from area mesh codes
-- Various use cases and output formats

-- Example 1: Basic polygon creation with sample data
WITH sample_meshcodes AS (
  SELECT meshcode, description FROM UNNEST([
    STRUCT('5339', '一次メッシュ（約80km）'),
    STRUCT('533945', '二次メッシュ（約10km）'),
    STRUCT('********', '三次メッシュ（約1km）'),
    STRUCT('********1', '四次メッシュ（約500m）'),
    STRUCT('********11', '五次メッシュ（約250m）'),
    STRUCT('********111', '1/8次メッシュ（約125m）')
  ])
),

mesh_with_polygons AS (
  SELECT 
    s.meshcode,
    s.description,
    coord_data.mesh_scale,
    coord_data.center_lat,
    coord_data.center_lon,
    -- Create polygon using WKT format
    ST_GEOGFROMTEXT(
      CONCAT(
        'POLYGON((',
        CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING), ',',
        CAST(coord_data.boundary_lon_max AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING), ',',
        CAST(coord_data.boundary_lon_max AS STRING), ' ', CAST(coord_data.boundary_lat_max AS STRING), ',',
        CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_max AS STRING), ',',
        CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING),
        '))'
      )
    ) AS polygon_geom,
    coord_data.boundary_lat_min,
    coord_data.boundary_lon_min,
    coord_data.boundary_lat_max,
    coord_data.boundary_lon_max
  FROM sample_meshcodes s,
  UNNEST(`beaconbank-analytics.UDF.areamesh2latlon_finer`(s.meshcode)) AS coord_data
)

SELECT 
  meshcode,
  description,
  mesh_scale,
  center_lat,
  center_lon,
  polygon_geom,
  ST_AREA(polygon_geom) AS area_square_meters,
  ROUND(ST_AREA(polygon_geom) / 1000000, 6) AS area_square_kilometers,
  -- Get polygon as WKT text
  ST_ASTEXT(polygon_geom) AS polygon_wkt,
  -- Get polygon as GeoJSON
  ST_ASGEOJSON(polygon_geom) AS polygon_geojson
FROM mesh_with_polygons
ORDER BY LENGTH(meshcode), meshcode;

-- Example 2: Create a table with mesh polygons (uncomment to use)
/*
CREATE OR REPLACE TABLE `your_project.your_dataset.mesh_polygons` AS
WITH mesh_data AS (
  -- Replace this with your actual mesh code source
  SELECT meshcode 
  FROM `your_project.your_dataset.your_mesh_table`
  WHERE meshcode IS NOT NULL
)

SELECT 
  meshcode,
  coord_data.mesh_scale,
  coord_data.center_lat,
  coord_data.center_lon,
  ST_GEOGFROMTEXT(
    CONCAT(
      'POLYGON((',
      CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING), ',',
      CAST(coord_data.boundary_lon_max AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING), ',',
      CAST(coord_data.boundary_lon_max AS STRING), ' ', CAST(coord_data.boundary_lat_max AS STRING), ',',
      CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_max AS STRING), ',',
      CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING),
      '))'
    )
  ) AS polygon_geom,
  ST_AREA(
    ST_GEOGFROMTEXT(
      CONCAT(
        'POLYGON((',
        CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING), ',',
        CAST(coord_data.boundary_lon_max AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING), ',',
        CAST(coord_data.boundary_lon_max AS STRING), ' ', CAST(coord_data.boundary_lat_max AS STRING), ',',
        CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_max AS STRING), ',',
        CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING),
        '))'
      )
    )
  ) AS area_square_meters
FROM mesh_data,
UNNEST(`beaconbank-analytics.UDF.areamesh2latlon_finer`(meshcode)) AS coord_data;
*/

-- Example 3: Find mesh polygons that intersect with a specific point
/*
WITH target_point AS (
  SELECT ST_GEOGPOINT(139.7671, 35.6812) AS point -- Tokyo Station coordinates
),

mesh_candidates AS (
  SELECT meshcode FROM UNNEST([
    '********', '********', '********', '********',
    '********', '********', '********', '********'
  ]) AS meshcode
)

SELECT 
  m.meshcode,
  coord_data.mesh_scale,
  ST_GEOGFROMTEXT(
    CONCAT(
      'POLYGON((',
      CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING), ',',
      CAST(coord_data.boundary_lon_max AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING), ',',
      CAST(coord_data.boundary_lon_max AS STRING), ' ', CAST(coord_data.boundary_lat_max AS STRING), ',',
      CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_max AS STRING), ',',
      CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING),
      '))'
    )
  ) AS polygon_geom,
  ST_CONTAINS(
    ST_GEOGFROMTEXT(
      CONCAT(
        'POLYGON((',
        CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING), ',',
        CAST(coord_data.boundary_lon_max AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING), ',',
        CAST(coord_data.boundary_lon_max AS STRING), ' ', CAST(coord_data.boundary_lat_max AS STRING), ',',
        CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_max AS STRING), ',',
        CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING),
        '))'
      )
    ),
    t.point
  ) AS contains_point
FROM mesh_candidates m,
UNNEST(`beaconbank-analytics.UDF.areamesh2latlon_finer`(m.meshcode)) AS coord_data,
target_point t
WHERE ST_CONTAINS(
  ST_GEOGFROMTEXT(
    CONCAT(
      'POLYGON((',
      CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING), ',',
      CAST(coord_data.boundary_lon_max AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING), ',',
      CAST(coord_data.boundary_lon_max AS STRING), ' ', CAST(coord_data.boundary_lat_max AS STRING), ',',
      CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_max AS STRING), ',',
      CAST(coord_data.boundary_lon_min AS STRING), ' ', CAST(coord_data.boundary_lat_min AS STRING),
      '))'
    )
  ),
  t.point
);
*/
